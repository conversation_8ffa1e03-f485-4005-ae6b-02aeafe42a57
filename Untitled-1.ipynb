{"cells": [{"cell_type": "markdown", "id": "559f998b", "metadata": {}, "source": ["#读取文件 \n", "with open(文件路径名) as 文件对象 #代码打开文件\n", "     for line in 文件对象 #逐行读取\n", "         print(line) #打印每行内容 \n", "         print(line.strip()) #不空行\n", "\n", "with open(文件路径名) as 文件对象 #代码打开文件\n", "     lines= 文件对象.readlines() #一次性读取每行内容\n", "print(lines) #打印全部内容\n", "print(lines.strip()) #不空行"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}
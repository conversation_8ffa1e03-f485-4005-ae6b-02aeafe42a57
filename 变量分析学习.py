import pandas as pd
import numpy as np

import matplotlib.pyplot as plt
import seaborn as sns

df=pd.read_csv(r'credit.data.csv')
print(df.info())

s1=df.columns
print(s1)

#数据清洗 缺失值
missing_rate=df.isnull().sum()/len(df)
col1=df.columns[missing_rate>0.3].tolist()
col2 = ['id', 'member_id', 'emp_title', 'issue_d', 'url', 'desc', 'title', 'zip_code']
drop_cols=set(col1+col2)
df.drop(columns=drop_cols,inplace=True)
print(df.info())


#目标变量转化_方法一
print(df['loan_status'])
print(df['loan_status'].unique())
bad_status=['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']
df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info())



#方法二

discrete_columns=[]
continuous_columns=[]
for col in df.columns:
   if df[col].dtype in ['int64','float64']:
      unique_ratio=df[col].nunique()/len(df)
      if unique_ratio<0.05:
         discrete_columns.append(col)
      else:
         continuous_columns.append(col)
   else:
      discrete_columns.append(col)




print('discrete columns:',discrete_columns)
print('continuous columns:',continuous_columns)

df_discrete=df[discrete_columns]
df_discrete[df_discrete.columns[:-1]].hist(figsize=(20, 20),bins=30,sharex=False, sharey=False)
plt.tight_layout()
plt.show()

#连续变量概率密度
df_continuous=df[continuous_columns]
if len(continuous_columns) > 0:
    # 动态计算网格大小
    n_cols = 4
    n_rows = (len(continuous_columns) + n_cols - 1) // n_cols  # 向上取整
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))

    # 如果只有一行，axes可能不是二维数组
    if n_rows == 1:
        axes = axes.reshape(1, -1)

    for i, feature in enumerate(continuous_columns):
        ax = axes[i // n_cols, i % n_cols]
        sns.histplot(df_continuous[feature],kde =True ,ax=ax)
        ax.set_title(feature)
        ax.set_xticklabels([])  # Hide x-axis labels

    # 隐藏多余的子图
    for i in range(len(continuous_columns), n_rows * n_cols):
        axes[i // n_cols, i % n_cols].set_visible(False)

plt.tight_layout()
plt.show()

from sklearn.preprocessing import StandardScaler
for col in df_continuous.columns:
   if df_continuous[col].skew()>1:
      df_continuous[col]=np.log1p(df_continuous[col])
   else:
       df_continuous[col] = StandardScaler() \
            .fit_transform(df_continuous[col].values.reshape(-1, 1)).flatten()
       


from pandas.api.types import is_numeric_dtype

def compute_woe_iv(df, feature, target, bins=10, smoothing=0.5):
   """
   不区别连续还是离散变量自动计算woe

   参数
   df       : pandas.DataFrame 数据框 包含特征以及目标变量
   feature  : str 特征
   target   : str 目标变量 
   bins     : int 分箱数量
   smoothing:float 平滑参数    
   
   return
   woe_df : dataframe
   iv : float
   """

   series=df[feature]
   if is_numeric_dtype(series) and series.nunique()>bins:
      binned=pd.qcut(series,q=bins,duplicates='drop')
   else:
      binned=series.astype(str)
   grouped=(
      df.assign(_bin=binned)
      .groupby('_bin',as_index=False)[target]
      .agg(N='count', bad='sum')
   )
   grouped['good']=grouped['N']-grouped['bad']
   total_good, total_bad = grouped['good'].sum(), grouped['bad'].sum()
   grouped['bad_rate']=grouped['bad'].clip(lower=smoothing)/total_bad
   grouped['good_rate']=grouped['good'].clip(lower=smoothing)/total_good
   grouped['woe']=np.log(grouped['bad_rate']/grouped['good_rate'])
   grouped['woe']=grouped['woe'].replace([np.inf,-np.inf],0)
   grouped['iv']=grouped['woe']*(grouped['bad_rate']-grouped['good_rate'])
   iv=grouped['iv'].sum()
   woe_df=(
      grouped
      .rename(columns={'_bin':'bin'})
      [['bin','N','bad','good','bad_rate','good_rate','woe','iv']]
      .sort_values(by='woe')
      .reset_index(drop=True)

     )
   return woe_df, iv

iv_dict={}
woe_dict={}

for col in df.columns:
   if col=='loan_status_new':  # 跳过目标变量
      continue
   woe_df,iv=compute_woe_iv(df,col,'loan_status_new',bins=10,smoothing=0.5)
   iv_dict[col]=iv
   woe_dict[col]=woe_df
  
selected_feats = [
   f for f, iv in iv_dict.items() 
   if(f in discrete_columns and iv>0.3 )
   or (f in continuous_columns and iv>0.5)
]
print("选中特征：", selected_feats)

import pandas as pd
import numpy as np     
import matplotlib.pyplot as plt
import seaborn as sns

df=pd.read_csv(r'loan_data_2007_2014.csv')
print(df.info(10))
df.describe()


#列名index
df_feature=df.columns
print(df_feature)
df_target=df['loan_status']
print(df_target)

#定义目标变量
df_target=df['loan_status']
bad_status=['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']
df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info())

#缺失值删去缺失率大于0.3
index_high_missing=[]
for col in df.columns:
   missing_rate=df[col].isnull().sum()/len(df)
   if missing_rate<0.3:
      continue
   else:
    index_high_missing.append(col)
print(index_high_missing)
df=df.drop(columns=index_high_missing)
print(df.info())


#区别连续变量和离散变量
discrete_columns=[]
continuous_columns=[]
for col in df.columns:
   if df[col].dtype in ['int64','float64']:
      unique_ratio=df[col].nunique()/len(df)
      if unique_ratio<0.05:
         discrete_columns.append(col)
      else:
         continuous_columns.append(col)
   else:
      discrete_columns.append(col)
print('discrete columns:',discrete_columns)
print('continuous columns:',continuous_columns)

#离散变量可视化
df_discrete=df[discrete_columns]
df_discrete[df_discrete.columns[:-1]].hist(figsize=(20, 20),bins=30,sharex=False, sharey=False)
plt.tight_layout()
plt.show()



#连续变量可视化
import seaborn as sns
df_contiunous=df[continuous_columns]
fig, axes = plt.subplots(3, 4, figsize=(20, 15), squeeze=False)
for i,col in enumerate(continuous_columns):
   row=i//4
   col=i%4
   ax=axes[row,col]
   sns.histplot(df_contiunous[col],kde=True,ax=ax)
   ax.set_title(col)
   ax.set_xticklabels([])
plt.tight_layout()
plt.show()


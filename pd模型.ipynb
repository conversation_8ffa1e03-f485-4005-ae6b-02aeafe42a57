import pandas as pd
import numpy as np     
import matplotlib.pyplot as plt
import seaborn as sns

df=pd.read_csv(r'loan_data_2007_2014.csv')
print(df.info(10))
df.describe()


#列名index
df_feature=df.columns
print(df_feature)
df_target=df['loan_status']
print(df_target)

#定义目标变量
df_target=df['loan_status']
bad_status=['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']
df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info())

#缺失值
index_high_missing=[]
for col in df.columns:
   missing_rate=df[col].isnull().sum()/len(df)
   if missing_rate<0.3:
      continue
   else:
    index_high_missing.append(col)
print(index_high_missing)

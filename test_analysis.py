import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 测试数据读取
print("正在读取数据...")
df = pd.read_csv('credit.data.csv')
print(f"数据形状: {df.shape}")
print(f"列数: {len(df.columns)}")

# 测试数据清洗
print("\n正在进行数据清洗...")
missing_rate = df.isnull().sum() / len(df)
col1 = df.columns[missing_rate > 0.3].tolist()
col2 = ['id', 'member_id', 'emp_title', 'issue_d', 'url', 'desc', 'title', 'zip_code']
drop_cols = set(col1 + col2)
print(f"将删除 {len(drop_cols)} 列")

df.drop(columns=drop_cols, inplace=True)
print(f"清洗后数据形状: {df.shape}")

# 测试目标变量转化
print("\n正在转化目标变量...")
print("loan_status 唯一值:")
print(df['loan_status'].unique())

bad_status = ['Charged Off', 'Default', 'Late (31-120 days)', 'Does not meet the credit policy. Status:Charged Off']
df['loan_status_new'] = df['loan_status'].apply(lambda x: 1 if x in bad_status else 0)
df.drop(columns='loan_status', inplace=True)
print(f"目标变量分布:\n{df['loan_status_new'].value_counts()}")

print("\n✅ 基础数据处理完成！")

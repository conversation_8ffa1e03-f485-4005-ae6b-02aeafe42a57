import numpy as np
a=np.array([1,2,3]) #创建一维数组
print(a)

b=np.array([[1,2,3],[4,5,6]]) #创建二维数组
print(b)    

c=np.zeros((2,3))
print(c)

d=np.ones((3,4))
print(d)

e=np.eye((2))
print(e)

f=np.arange(0,10,1) #区间以及间隔
print(f)

g=np.linspace(0,10,5) #区间以及等分数
print(g)

r1=np.random.rand(5) #(0,1)中随机数5
r2=np.random.rand(1,2) #在（0，1）中随机生成一个2行1列的二维数组
r3=np.random.randn(5)
r4=np.random.uniform(low=5,high=20,size=5)
r5=np.random.uniform(low=10,high=30,size=(2,3))
r6=np.random.normal(loc=10,scale=10,size=5)
r7=np.random.randint(low=0,high=20,size=5)

a=np.arange(10)
np.random.shuffle(a) #打乱数组顺序


#属性
r8=np.array([[1,2,3],[4,5,6]])
#print(r8.dtype)

b=np.zeros(3)
#print(b)


c=np.array([[1,2,3],[4,5,6],[7,8,9]])
d=c[1:][1:]
e=c[1:,:1]
print(e)


arr=np.array([1,2,3,np.nan,6,7,8])
arr[np.isnan(arr)]

arr2=np.array([1,2,3,6,7,8])
b=np.where(arr2>1,4,0)
print(b)

c=df.isnan()
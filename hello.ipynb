{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3cd4e02a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Desktop/python/.venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020\n", "  warnings.warn(\n"]}, {"ename": "HTTPError", "evalue": "404 Client Error: Not Found for url: https://finance.yahoo.com/quote/AAPL/financials/", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 32\u001b[0m\n\u001b[1;32m     29\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✅ 已保存：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mout_path\u001b[38;5;241m.\u001b[39mresolve()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 32\u001b[0m     \u001b[43msave_to_excel\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mAAPL\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mrequests\u001b[39;00m\n\u001b[1;32m     34\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpd\u001b[39;00m\n", "Cell \u001b[0;32mIn[1], line 24\u001b[0m, in \u001b[0;36msave_to_excel\u001b[0;34m(ticker, out_dir)\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msave_to_excel\u001b[39m(ticker: \u001b[38;5;28mstr\u001b[39m, out_dir: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m---> 24\u001b[0m     statements \u001b[38;5;241m=\u001b[39m \u001b[43mfetch_yahoo_financials\u001b[49m\u001b[43m(\u001b[49m\u001b[43mticker\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     25\u001b[0m     out_path \u001b[38;5;241m=\u001b[39m Path(out_dir) \u001b[38;5;241m/\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mticker\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_financials.xlsx\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     26\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mExcelWriter(out_path, engine\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mopenpyxl\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m writer:\n", "Cell \u001b[0;32mIn[1], line 9\u001b[0m, in \u001b[0;36mfetch_yahoo_financials\u001b[0;34m(ticker)\u001b[0m\n\u001b[1;32m      7\u001b[0m headers \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser-Agent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMozilla/5.0\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[1;32m      8\u001b[0m resp \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mget(url, headers\u001b[38;5;241m=\u001b[39mheaders)\n\u001b[0;32m----> 9\u001b[0m \u001b[43mresp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     10\u001b[0m tables \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_html(resp\u001b[38;5;241m.\u001b[39mtext, flavor\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlxml\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     12\u001b[0m result \u001b[38;5;241m=\u001b[39m {}\n", "File \u001b[0;32m~/Desktop/python/.venv/lib/python3.9/site-packages/requests/models.py:1026\u001b[0m, in \u001b[0;36mResponse.raise_for_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1021\u001b[0m     http_error_msg \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m   1022\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstatus_code\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m Server Error: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mreason\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m for url: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murl\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1023\u001b[0m     )\n\u001b[1;32m   1025\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http_error_msg:\n\u001b[0;32m-> 1026\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HTTPError(http_error_msg, response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m)\n", "\u001b[0;31mHTTPError\u001b[0m: 404 Client Error: Not Found for url: https://finance.yahoo.com/quote/AAPL/financials/"]}], "source": ["import requests\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "def fetch_yahoo_financials(ticker: str) -> dict:\n", "    url = f\"https://finance.yahoo.com/quote/{ticker}/financials\"\n", "    headers = {\"User-Agent\": \"Mozilla/5.0\"}\n", "    resp = requests.get(url, headers=headers)\n", "    resp.raise_for_status()\n", "    tables = pd.read_html(resp.text, flavor=\"lxml\")\n", "\n", "    result = {}\n", "    if len(tables) >= 3:\n", "        names = [\"Income Statement\", \"Balance Sheet\", \"Cash Flow\"]\n", "        for idx, name in enumerate(names):\n", "            df = tables[idx].dropna(axis=1, how=\"all\")\n", "            result[name] = df\n", "    else:\n", "        for idx, df in enumerate(tables):\n", "            result[f\"Table {idx}\"] = df.dropna(axis=1, how=\"all\")\n", "    return result\n", "\n", "def save_to_excel(ticker: str, out_dir: str = \".\"):\n", "    statements = fetch_yahoo_financials(ticker)\n", "    out_path = Path(out_dir) / f\"{ticker}_financials.xlsx\"\n", "    with pd.ExcelWriter(out_path, engine=\"openpyxl\") as writer:\n", "        for sheet_name, df in statements.items():\n", "            df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "    print(f\"✅ 已保存：{out_path.resolve()}\")\n", "\n", "if __name__ == \"__main__\":\n", "    save_to_excel(\"AAPL\")\n", "import requests\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "def fetch_yahoo_financials(ticker: str) -> dict:\n", "    url = f\"https://finance.yahoo.com/quote/{ticker}/financials\"\n", "    headers = {\"User-Agent\": \"Mozilla/5.0\"}\n", "    resp = requests.get(url, headers=headers)\n", "    resp.raise_for_status()\n", "    tables = pd.read_html(resp.text, flavor=\"lxml\")\n", "\n", "    result = {}\n", "    if len(tables) >= 3:\n", "        names = [\"Income Statement\", \"Balance Sheet\", \"Cash Flow\"]\n", "        for idx, name in enumerate(names):\n", "            df = tables[idx].dropna(axis=1, how=\"all\")\n", "            result[name] = df\n", "    else:\n", "        for idx, df in enumerate(tables):\n", "            result[f\"Table {idx}\"] = df.dropna(axis=1, how=\"all\")\n", "    return result\n", "\n", "def save_to_excel(ticker: str, out_dir: str = \".\"):\n", "    statements = fetch_yahoo_financials(ticker)\n", "    out_path = Path(out_dir) / f\"{ticker}_financials.xlsx\"\n", "    with pd.ExcelWriter(out_path, engine=\"openpyxl\") as writer:\n", "        for sheet_name, df in statements.items():\n", "            df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "    print(f\"✅ 已保存：{out_path.resolve()}\")\n", "\n", "if __name__ == \"__main__\":\n", "    save_to_excel(\"AAPL\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}
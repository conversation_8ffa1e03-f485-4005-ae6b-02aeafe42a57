
import pandas as pd
import numpy as np

import matplotlib.pyplot as plt
import seaborn as sns

df=pd.read_csv(r'credit.data.csv')
print(df.info())

s1=df.columns
print(s1)

#数据清洗 缺失值
missing_rate=df.isnull().sum()/len(df)
col1=df.columns[missing_rate>0.3].tolist()
col2 = ['id', 'member_id', 'emp_title', 'issue_d', 'url', 'desc', 'title', 'zip_code']
drop_cols=set(col1+col2)
df.drop(columns=drop_cols,inplace=True)
print(df.info())


#目标变量转化_方法一
print(df['loan_status'])
print(df['loan_status'].unique())
bad_status=['Charged Off','Default','Late (31-120 days)''Does not meet the credit policy. Status:Charged Off']
df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info())

#目标变量转化_方法二
print(df['loan_status'].unique)
bad_status=['Charged Off','Default','Late (31-120 days)''Does not meet the credit policy. Status:Charged Off']
mapping={x:1 if x in bad_status else 0 for x in df['loan_status']}
df['loan_status_new']=df['loan_status'].map(mapping).fillna(0).astype(int)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info()    


#判断特征类型 
feature_type={}
for col in df.columns:
   if df[col].dtype in ['int64','float64']:
      unique_ratio=df[col].nunique()/len(df)
      if unique_ratio<0.05:
         feature_type[col]='discrete'
      else:
         feature_type[col]='continuous'
   else:
      feature_type[col]='discrete'  
discrete_cols=[col for col,value in feature_type.items() if value=='discrete']
continuous_cols=[col for col,value in feature_type.items() if value=='continuous']  
print(discrete_cols)
print(continuous_cols)

#方法二

discrete_columns=[]
continuous_columns=[]
for col in df.columns:
   if df[col].dtype in ['int64','float64']:
      unique_ratio=df[col].nunique()/len(df)
      if unique_ratio<0.05:
         discrete_columns.append(col)
      else:
         continuous_columns.append(col)
   else:
      discrete_columns.append(col)

#离散变量直方图
print('discrete columns:',discrete_columns)
print('continuous columns:',continuous_columns)

df_discrete=df[discrete_columns]
df_discrete[df_discrete.columns[:-1]].hist(figsize=(20, 20),bins=30,sharex=False, sharey=False)
plt.tight_layout()
plt.show()

#连续变量概率密度
fig, axes = plt.subplots(3, 4, figsize=(20, 15))  # Adjust the grid size for 12 continuous features
for i, feature in enumerate(continuous_features):
    ax = axes[i // 4, i % 4]  # Adjust the indices for a 3x4 grid
    sns.histplot(df_continuous[feature],kde =True ,ax=ax)
    ax.set_title(feature)
    ax.set_xticklabels([])  # Hide x-axis labels

plt.tight_layout()
plt.show